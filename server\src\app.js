import 'dotenv/config'

import express from 'express'
import { appMiddlewares } from './middleware/appMiddlewares'
import registerRoutes from './utils/registerRoutes'
import { initializeDatabase } from './config/database'
import errorMiddleware from './middleware/errorMiddleware'
import { env } from './utils/env'

async function createApp() {
  // Connect to the database first
  await initializeDatabase()

  const port = process.env.PORT
  const app = express()

  app.use(express.urlencoded({ limit: '50mb', extended: true }))
  app.use(express.json({ limit: '50mb' }))

  app.get('/', (req, res) => {
    res.send(`MRH Rest API`)
  })

  appMiddlewares(app)
  registerRoutes(app)

  app.use('*', (req, res) => {
    res.notFound()
  })

  /**
   * This is placed at the last middleware declaration
   */
  app.use(errorMiddleware)

  return { app, port }
}

try {
  void createApp().then(({ app, port }) => {
    app.listen(port, () => {
      console.log(`Server running in http://localhost:${port}`)
    })
  })
} catch (error) {
  console.error('WE OUTPUT THE ERROR HERE', error.message)
  process.exit(1)
}
