export default (IModel, populate) => async (req, res, next) => {
  let query

  const excludeKeys = ['select', 'sort', 'page', 'limit']
  const filteredQuery = { ...req.query }

  const queryString = JSON.stringify(
    Object.keys(filteredQuery).reduce((acc, key) => {
      if (!excludeKeys.includes(key)) {
        acc[key] = filteredQuery[key]
      }
      return acc
    }, {})
  )

  query = IModel.find(JSON.parse(queryString))

  query = query.select('-password -__v')

  // Selecting specific fields
  if (req.query.select) {
    if (typeof req.query.select === 'string') {
      const selectString = req.query.select.split(',').join(' ')
      query = query.select(selectString)
    }
  }

  // Sort the results
  if (req.query.sort) {
    if (typeof req.query.sort === 'string') {
      const sortString = req.query.sort.split(',').join(' ')
      query = query.sort(sortString)
    }
  } else {
    // Default sort results
    query = query.sort('-cratedAt')
  }

  // Pagination area
  const page = parseInt(req.query.page) || 1
  const limit = parseInt(req.query.limit) || 10
  const start = (page - 1) * limit
  const end = start + limit
  const count = await IModel.countDocuments()

  query = query.skip(start).limit(limit)

  let data
  if (populate && (populate !== undefined || populate !== null)) {
    data = query.populate(populate)
  }

  data = await query

  const pagination = {}

  if (start > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    }
  }

  if (end < count) {
    pagination.next = {
      page: page + 1,
      limit
    }
  }

  res.data = {
    pagination,
    itemCount: data.length,
    total: count,
    items: data
  }

  next()
}
