import ResourceError from '../common/errors/ResourceError'

const errorMiddleware = (err, req, res, next) => {
  const _error = { ...err }

  _error.message = err.message

  // ID not found
  if (_error.kind === 'ObjectId') {
    next(new ResourceError(_error.kind, _error.value))
    return
  }

  // Duplicate error
  if (_error.code === 11000) {
    const objectRegEx = /dup key: {(.+?)}/
    const errorMessage = _error.message
    const match = errorMessage.match(objectRegEx)

    if (match !== undefined || match !== null) {
      const objectString = match[1].replace(/\\"/g, "'").trim()
      res.error(`The ${objectString} already exists`, 409)
      return
    }
    res.error(_error.message, 409)
    return
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    const message = Object.values(_error.errors).map((val) => {
      let msg = `The field '${val.path}' is `

      if (val.kind === 'regexp') {
        msg += 'invalid'
      } else {
        msg += val.kind
      }

      return msg
    })
    res.response({ errors: { message } }, 400)
    return
  }

  // Send error response
  res.error(
    _error.message,
    _error.statusCode !== undefined || _error.statusCode !== null
      ? _error.statusCode
      : 500
  )
}

export default errorMiddleware
