import { Router } from 'express'
import asyncHandler from '../../common/handlers/asyncHandler'
import {
  createUser,
  deleteUser,
  readAllUser,
  readUser,
  updateUser
} from '../../controller/usersController'
import requestMiddleware from '../../middleware/requestMiddleware'
import User from '../../models/User'
import { isAuthenticated } from '../../middleware/authMiddleware'

const router = Router()

router.get(
  '/',
  isAuthenticated,
  requestMiddleware(User),
  asyncHandler(readAllUser)
)
router.post('/', asyncHandler(createUser))
router.get('/:userID', asyncHandler(readUser))
router.put('/:userID', asyncHandler(updateUser))
router.delete('/:userID', asyncHandler(deleteUser))

export default router
