export const hasMessage = (data) => {
  return Object.prototype.hasOwnProperty.call(data, 'message')
}

export const isEmptyObject = (obj) => {
  return Object.keys(obj).length === 0
}

export const validateEmail = (email) => {
  const emailRegEx =
    /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@(([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]{2,})$/

  return email.length > 3 && emailRegEx.test(email)
}
